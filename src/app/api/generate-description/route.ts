import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const apiKey = process.env.OPENAI_API_KEY;
let openai: OpenAI | null = null;
if (apiKey) {
  openai = new OpenAI({ apiKey });
}

interface ProductInfo {
  name: string;
  category?: string;
  features?: string[];
  keywords?: string[];
  targetAudience?: string;
  additionalInfo?: string;
}

interface SeoContent {
  shortDescription: string;
  slug: string;
  wooCommerceMainDescription: string;
  wooCommerceShortDescription: string;
}

// Função para validar limites de caracteres SEO
const validateSeoLimits = (content: SeoContent): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Validar meta description (140-160 caracteres)
  if (content.shortDescription.length < 140 || content.shortDescription.length > 160) {
    errors.push(`Meta description deve ter entre 140-160 caracteres (atual: ${content.shortDescription.length})`);
  }

  // Validar título SEO (50-60 caracteres recomendado para o slug)
  if (content.slug.length > 60) {
    errors.push(`Slug muito longo (atual: ${content.slug.length} caracteres)`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Função para normalizar o slug
const generateSlug = (text: string): string => {
  const a = 'àáâäæãåāăąçćčđďèéêëēėęěğǵḧîïíīįìłḿñńǹňôöòóœøōõőṕŕřßśšşșťțûüùúūǘůűųẃẍÿýžźż·/_,:;';
  const b = 'aaaaaaaaaacccddeeeeeeeegghiiiiiilmnnnnoooooooooprrssssssttuuuuuuuuuwxyyzzz------';
  const p = new RegExp(a.split('').join('|'), 'g');

  return text.toString().toLowerCase()
    .replace(/\s+/g, '-') // Substituir espaços por -
    .replace(p, c => b.charAt(a.indexOf(c))) // Substituir caracteres especiais
    .replace(/&/g, '-and-') // Substituir & por '-and-'
    .replace(/[^\w\-]+/g, '') // Remover caracteres inválidos
    .replace(/\-\-+/g, '-') // Substituir múltiplos - por um único -
    .replace(/^-+/, '') // Remover - do início
    .replace(/-+$/, ''); // Remover - do fim
};


export async function POST(request: Request) {
  if (!openai) {
    return NextResponse.json(
      { error: 'A API da OpenAI não está configurada. Por favor, defina a variável de ambiente OPENAI_API_KEY.' },
      { status: 500 }
    );
  }

  try {
    const body = await request.json();
    let seoContent: SeoContent;

    if (body.action === 'generate') {
      seoContent = await generateSeoContent(body.productInfo);
    } else if (body.action === 'improve') {
      seoContent = await improveSeoContent(body.currentDescription, body.productInfo?.name);
    } else {
      return NextResponse.json({ error: 'Ação inválida. Use "generate" ou "improve".' }, { status: 400 });
    }

    return NextResponse.json({ seoContent });

  } catch (error) {
    console.error('Erro na API de descrição de produto:', error);
    return NextResponse.json({ error: 'Falha ao processar o pedido.' }, { status: 500 });
  }
}

async function generateSeoContent(productInfo: ProductInfo): Promise<SeoContent> {
  if (!openai) throw new Error('API da OpenAI não configurada');

  const prompt = `
    Analise as seguintes informações de um produto para uma loja WooCommerce em Portugal:
    - Nome do Produto: ${productInfo.name}
    ${productInfo.category ? `- Categoria: ${productInfo.category}` : ''}
    ${productInfo.features && productInfo.features.length > 0 ? `- Características: ${productInfo.features.join(', ')}` : ''}
    ${productInfo.keywords && productInfo.keywords.length > 0 ? `- Palavras-chave para SEO: ${productInfo.keywords.join(', ')}` : ''}
    ${productInfo.targetAudience ? `- Público-alvo: ${productInfo.targetAudience}` : ''}
    ${productInfo.additionalInfo ? `- Informações Adicionais: ${productInfo.additionalInfo}` : ''}

    Com base nisso, gere o seguinte conteúdo em português de Portugal, seguindo estritamente as regras para cada campo. A resposta DEVE ser um objeto JSON válido.

    **IMPORTANTE:** A curta descrição deve ser uma versão resumida e específica da descrição principal, não um texto genérico. Extraia as características mais importantes da descrição completa.

    1.  **wooCommerceMainDescription (Descrição WooCommerce):**
        -   **Objetivo:** Ser a descrição completa do produto no WooCommerce. Deve ser rica em detalhes e otimizada para conversão.
        -   **Estilo:** Use formatação HTML estruturada para melhor apresentação visual.
        -   **Tom:** Abrangente, informativo e persuasivo.
        -   **Formato OBRIGATÓRIO:** Use esta estrutura HTML:
            1. Parágrafo de abertura com tag p
            2. Título "Características principais:" em strong
            3. Características em parágrafos separados (sem listas ul/li)
            4. Parágrafo de fecho com tag p
        -   **Formatação das características:** Cada característica deve ser um parágrafo separado, claro e específico, destacando um benefício único. Use linguagem persuasiva e técnica quando apropriado.
        -   **Exemplo de estrutura:**
            <p>Parágrafo introdutório...</p>
            <strong>Características principais:</strong>
            <p><strong>Nome da característica:</strong> Descrição detalhada do benefício</p>
            <p><strong>Segunda característica:</strong> Explicação clara e persuasiva</p>
            <p><strong>Terceira característica:</strong> Mais detalhes específicos</p>
            <p>Parágrafo de fecho...</p>
        -   **Tamanho:** Entre 200-400 palavras para ser completa mas não excessiva.

    2.  **wooCommerceShortDescription (Curta Descrição WooCommerce):**
        -   **Objetivo:** Ser uma versão resumida e específica da descrição principal em formato de texto corrido.
        -   **Estilo:** Um parágrafo conciso que sintetiza os pontos mais importantes da descrição completa.
        -   **Tom:** Direto, específico e focado nos benefícios únicos deste produto em particular.
        -   **Formato:** Texto corrido em parágrafo simples, SEM formatação HTML, SEM listas.
        -   **Conteúdo:** Deve resumir as principais características e benefícios mencionados na descrição completa.
        -   **Importante:** NÃO use frases genéricas. Seja específico sobre este produto e suas características únicas.
        -   **Tamanho:** 2-3 frases que capturem a essência do produto e seus principais benefícios.

    3.  **shortDescription (Descrição SEO):**
        -   **Objetivo:** Meta description para maximizar a taxa de cliques (CTR) nos resultados de pesquisa do Google. Este texto aparecerá debaixo do título da página nos resultados do Google.
        -   **Estilo:** Uma ou duas frases diretas e apelativas que resumam o produto e o seu principal benefício. Deve incluir a palavra-chave principal de forma natural.
        -   **Tom:** Informativo e convidativo.
        -   **Tamanho:** OBRIGATORIAMENTE entre 140 e 160 caracteres. Este é um requisito técnico CRÍTICO para aparecer corretamente no Google e não pode ser ignorado. Conte os caracteres cuidadosamente.

    4.  **slug (Slug):**
        -   **Objetivo:** Criar um URL amigável para SEO.
        -   **Estilo:** Use o nome do produto como base. Converta para minúsculas, substitua espaços por hífens e remova "stop words" (ex: de, para, o, a) e caracteres especiais.
        -   **Exemplo:** Se o nome for "Sapatos de Couro para Homem", o slug deve ser "sapatos-couro-homem".

    Responda APENAS com o objeto JSON, formatado da seguinte maneira:
    {
      "wooCommerceMainDescription": "(texto aqui)",
      "wooCommerceShortDescription": "(texto aqui)",
      "shortDescription": "(texto aqui)",
      "slug": "(slug aqui)"
    }
  `;

  const response = await openai.chat.completions.create({
    model: "gpt-4o",
    messages: [
      {
        role: "system",
        content: "Você é um especialista em SEO e copywriting para e-commerce, focado em criar conteúdo otimizado para produtos WooCommerce em Portugal. Sua tarefa é gerar um conjunto de conteúdos (descrição WooCommerce, curta descrição WooCommerce, descrição SEO e slug) a partir de informações do produto. A resposta deve ser sempre um objeto JSON válido."
      },
      {
        role: "user",
        content: prompt
      }
    ],
    temperature: 0.6,
    max_tokens: 1200,
    response_format: { type: "json_object" },
  });

  const content = response.choices[0].message.content;
  if (!content) throw new Error("A resposta da API está vazia.");

  try {
    const parsedContent: SeoContent = JSON.parse(content);

    // Garante que o slug está bem formatado, mesmo que a IA falhe
    parsedContent.slug = generateSlug(parsedContent.slug || productInfo.name);

    // Validar limites de SEO
    const validation = validateSeoLimits(parsedContent);
    if (!validation.isValid) {
      console.warn("Limites de SEO não respeitados:", validation.errors);

      // Tentar corrigir a meta description se estiver fora dos limites
      if (parsedContent.shortDescription.length < 140) {
        // Se muito curta, expandir ligeiramente
        parsedContent.shortDescription = parsedContent.shortDescription + " Descubra mais detalhes.";
      } else if (parsedContent.shortDescription.length > 160) {
        // Se muito longa, truncar mantendo sentido
        parsedContent.shortDescription = parsedContent.shortDescription.substring(0, 157) + "...";
      }
    }

    return parsedContent;
  } catch (e) {
    console.error("Falha ao analisar JSON da API:", content);
    throw new Error("A resposta da API não é um JSON válido.");
  }
}

async function improveSeoContent(currentDescription: string, productName?: string): Promise<SeoContent> {
  if (!openai) throw new Error('API da OpenAI não configurada');

  const prompt = `
    Analise a seguinte descrição de produto (e o seu nome, se disponível) de uma loja WooCommerce em Portugal:
    - Nome do Produto: ${productName || 'Não fornecido'}
    - Descrição Atual: "${currentDescription}"

    Com base nisso, reescreva e otimize o conteúdo, gerando os seguintes elementos em português de Portugal. A resposta DEVE ser um objeto JSON válido.

    **IMPORTANTE:** A curta descrição deve ser uma versão resumida e específica da descrição principal, não um texto genérico. Extraia as características mais importantes da descrição completa.

    1.  **wooCommerceMainDescription (Descrição WooCommerce):**
        -   **Objetivo:** Ser a descrição completa do produto no WooCommerce. Deve ser rica em detalhes e otimizada para conversão.
        -   **Estilo:** Use formatação HTML estruturada para melhor apresentação visual.
        -   **Tom:** Abrangente, informativo e persuasivo.
        -   **Formato OBRIGATÓRIO:** Use esta estrutura HTML:
            1. Parágrafo de abertura com tag p
            2. Título "Características principais:" em strong
            3. Características em parágrafos separados (sem listas ul/li)
            4. Parágrafo de fecho com tag p
        -   **Formatação das características:** Cada característica deve ser um parágrafo separado, claro e específico, destacando um benefício único. Use linguagem persuasiva e técnica quando apropriado.
        -   **Exemplo de estrutura:**
            <p>Parágrafo introdutório...</p>
            <strong>Características principais:</strong>
            <p><strong>Nome da característica:</strong> Descrição detalhada do benefício</p>
            <p><strong>Segunda característica:</strong> Explicação clara e persuasiva</p>
            <p><strong>Terceira característica:</strong> Mais detalhes específicos</p>
            <p>Parágrafo de fecho...</p>
        -   **Tamanho:** Entre 200-400 palavras para ser completa mas não excessiva.

    2.  **wooCommerceShortDescription (Curta Descrição WooCommerce):**
        -   **Objetivo:** Ser uma versão resumida e específica da descrição principal, destacando os pontos mais importantes do produto específico.
        -   **Estilo:** Deve ser uma síntese da descrição principal, mantendo as características mais relevantes e específicas do produto em questão.
        -   **Tom:** Direto, específico e focado nos benefícios únicos deste produto em particular.
        -   **Formato:** Use parágrafos separados (sem listas ul/li) com 3-4 características principais extraídas da descrição completa.
        -   **Formatação obrigatória:** <p><strong>Característica específica 1:</strong> Benefício concreto</p><p><strong>Característica específica 2:</strong> Benefício concreto</p><p><strong>Característica específica 3:</strong> Benefício concreto</p>
        -   **Importante:** NÃO use frases genéricas. Seja específico sobre este produto em particular.
        -   **Tamanho:** Máximo 3-4 itens, cada um com características específicas do produto.

    3.  **shortDescription (Descrição SEO):**
        -   **Objetivo:** Criar uma meta description eficaz a partir do conteúdo existente para maximizar o CTR nos resultados de pesquisa do Google.
        -   **Estilo:** Uma ou duas frases diretas que resumam o produto e o seu benefício principal.
        -   **Tamanho:** OBRIGATORIAMENTE entre 140 e 160 caracteres. Este é um requisito técnico CRÍTICO para aparecer corretamente no Google e não pode ser ignorado. Conte os caracteres cuidadosamente.

    4.  **slug (Slug):**
        -   **Objetivo:** Gerar um slug de URL otimizado a partir do nome do produto ou, se não disponível, a partir da descrição.
        -   **Estilo:** Minúsculas, hífens para espaços, sem "stop words".

    Responda APENAS com o objeto JSON, formatado da seguinte maneira:
    {
      "wooCommerceMainDescription": "(texto aqui)",
      "wooCommerceShortDescription": "(texto aqui)",
      "shortDescription": "(texto aqui)",
      "slug": "(slug aqui)"
    }
  `;

  const response = await openai.chat.completions.create({
    model: "gpt-4o",
    messages: [
      {
        role: "system",
        content: "Você é um especialista em SEO e copywriting para e-commerce, focado em otimizar conteúdo de produtos WooCommerce em Portugal. Sua tarefa é reescrever uma descrição existente para criar um conjunto completo de conteúdos (descrição WooCommerce, curta descrição WooCommerce, descrição SEO e slug). A resposta deve ser sempre um objeto JSON válido."
      },
      {
        role: "user",
        content: prompt
      }
    ],
    temperature: 0.6,
    max_tokens: 1200,
    response_format: { type: "json_object" },
  });

  const content = response.choices[0].message.content;
  if (!content) throw new Error("A resposta da API está vazia.");

  try {
    const parsedContent: SeoContent = JSON.parse(content);

    // Garante que o slug está bem formatado
    parsedContent.slug = generateSlug(parsedContent.slug || productName || currentDescription.substring(0, 50));

    // Validar limites de SEO
    const validation = validateSeoLimits(parsedContent);
    if (!validation.isValid) {
      console.warn("Limites de SEO não respeitados:", validation.errors);

      // Tentar corrigir a meta description se estiver fora dos limites
      if (parsedContent.shortDescription.length < 140) {
        // Se muito curta, expandir ligeiramente
        parsedContent.shortDescription = parsedContent.shortDescription + " Descubra mais detalhes.";
      } else if (parsedContent.shortDescription.length > 160) {
        // Se muito longa, truncar mantendo sentido
        parsedContent.shortDescription = parsedContent.shortDescription.substring(0, 157) + "...";
      }
    }

    return parsedContent;
  } catch (e) {
    console.error("Falha ao analisar JSON da API:", content);
    throw new Error("A resposta da API não é um JSON válido.");
  }
}
